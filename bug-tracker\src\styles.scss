/* ===== GLOBAL STYLES - BUG TRACKER PRO ===== */

/* ===== CSS CUSTOM PROPERTIES (DESIGN TOKENS) ===== */
:root {
  /* === Color Palette === */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  /* Status Colors */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;

  --color-info-50: #f0f9ff;
  --color-info-100: #e0f2fe;
  --color-info-500: #06b6d4;
  --color-info-600: #0891b2;
  --color-info-700: #0e7490;

  /* Neutral Colors */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* === Typography === */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* === Spacing === */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  --spacing-24: 6rem;     /* 96px */

  /* === Border Radius === */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;   /* 2px */
  --border-radius-base: 0.25rem;  /* 4px */
  --border-radius-md: 0.375rem;   /* 6px */
  --border-radius-lg: 0.5rem;     /* 8px */
  --border-radius-xl: 0.75rem;    /* 12px */
  --border-radius-2xl: 1rem;      /* 16px */
  --border-radius-full: 9999px;

  /* === Shadows === */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* === Z-Index === */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* === Transitions === */
  --transition-fast: 150ms ease-in-out;
  --transition-base: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;

  /* === Breakpoints === */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-4);
  color: var(--color-gray-700);
}

a {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: color var(--transition-fast);

  &:hover {
    color: var(--color-primary-700);
    text-decoration: underline;
  }

  &:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
  }
}

code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--color-gray-100);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-base);
  color: var(--color-gray-800);
}

/* ===== FORM ELEMENTS ===== */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

button {
  font-family: inherit;
  cursor: pointer;
}

/* Remove default button styles */
button, input[type="button"], input[type="reset"], input[type="submit"] {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  cursor: pointer;
}

/* ===== UTILITY CLASSES ===== */

/* Display */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Flexbox */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }
.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-none { flex: none !important; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }
.gap-1 { gap: var(--spacing-1) !important; }
.gap-2 { gap: var(--spacing-2) !important; }
.gap-3 { gap: var(--spacing-3) !important; }
.gap-4 { gap: var(--spacing-4) !important; }
.gap-6 { gap: var(--spacing-6) !important; }
.gap-8 { gap: var(--spacing-8) !important; }

/* Spacing - Margin */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }
.ml-auto { margin-left: auto !important; }
.mr-auto { margin-right: auto !important; }
.mt-0 { margin-top: 0 !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mb-0 { margin-bottom: 0 !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }

/* Spacing - Padding */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }
.px-2 { padding-left: var(--spacing-2) !important; padding-right: var(--spacing-2) !important; }
.px-3 { padding-left: var(--spacing-3) !important; padding-right: var(--spacing-3) !important; }
.px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
.px-6 { padding-left: var(--spacing-6) !important; padding-right: var(--spacing-6) !important; }
.py-2 { padding-top: var(--spacing-2) !important; padding-bottom: var(--spacing-2) !important; }
.py-3 { padding-top: var(--spacing-3) !important; padding-bottom: var(--spacing-3) !important; }
.py-4 { padding-top: var(--spacing-4) !important; padding-bottom: var(--spacing-4) !important; }

/* Position */
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Width & Height */
.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.h-full { height: 100% !important; }
.h-auto { height: auto !important; }
.min-h-screen { min-height: 100vh !important; }

/* Text */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

/* Text Colors */
.text-primary { color: var(--color-primary-600) !important; }
.text-secondary { color: var(--color-secondary-600) !important; }
.text-success { color: var(--color-success-600) !important; }
.text-warning { color: var(--color-warning-600) !important; }
.text-error { color: var(--color-error-600) !important; }
.text-info { color: var(--color-info-600) !important; }
.text-gray-400 { color: var(--color-gray-400) !important; }
.text-gray-500 { color: var(--color-gray-500) !important; }
.text-gray-600 { color: var(--color-gray-600) !important; }
.text-gray-700 { color: var(--color-gray-700) !important; }
.text-gray-800 { color: var(--color-gray-800) !important; }
.text-gray-900 { color: var(--color-gray-900) !important; }

/* Background Colors */
.bg-white { background-color: var(--color-white) !important; }
.bg-gray-50 { background-color: var(--color-gray-50) !important; }
.bg-gray-100 { background-color: var(--color-gray-100) !important; }
.bg-gray-200 { background-color: var(--color-gray-200) !important; }
.bg-primary { background-color: var(--color-primary-600) !important; }
.bg-primary-50 { background-color: var(--color-primary-50) !important; }
.bg-success { background-color: var(--color-success-600) !important; }
.bg-success-50 { background-color: var(--color-success-50) !important; }
.bg-warning { background-color: var(--color-warning-600) !important; }
.bg-warning-50 { background-color: var(--color-warning-50) !important; }
.bg-error { background-color: var(--color-error-600) !important; }
.bg-error-50 { background-color: var(--color-error-50) !important; }

/* Border */
.border { border: 1px solid var(--color-gray-200) !important; }
.border-0 { border: 0 !important; }
.border-gray-200 { border-color: var(--color-gray-200) !important; }
.border-gray-300 { border-color: var(--color-gray-300) !important; }
.border-primary { border-color: var(--color-primary-600) !important; }
.border-error { border-color: var(--color-error-600) !important; }
.rounded { border-radius: var(--border-radius-base) !important; }
.rounded-md { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-full { border-radius: var(--border-radius-full) !important; }

/* Shadow */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-base) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* ===== COMPONENT STYLES ===== */

/* === Buttons === */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;

  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--color-primary-500);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  /* Button Sizes */
  &.btn-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
  }

  &.btn-lg {
    padding: var(--spacing-4) var(--spacing-6);
    font-size: var(--font-size-base);
  }

  /* Button Variants */
  &.btn-primary {
    background-color: var(--color-primary-600);
    color: var(--color-white);
    border-color: var(--color-primary-600);

    &:hover:not(:disabled) {
      background-color: var(--color-primary-700);
      border-color: var(--color-primary-700);
    }

    &:active {
      background-color: var(--color-primary-800);
      border-color: var(--color-primary-800);
    }
  }

  &.btn-secondary {
    background-color: var(--color-white);
    color: var(--color-gray-700);
    border-color: var(--color-gray-300);

    &:hover:not(:disabled) {
      background-color: var(--color-gray-50);
      border-color: var(--color-gray-400);
    }

    &:active {
      background-color: var(--color-gray-100);
    }
  }

  &.btn-success {
    background-color: var(--color-success-600);
    color: var(--color-white);
    border-color: var(--color-success-600);

    &:hover:not(:disabled) {
      background-color: var(--color-success-700);
      border-color: var(--color-success-700);
    }
  }

  &.btn-warning {
    background-color: var(--color-warning-600);
    color: var(--color-white);
    border-color: var(--color-warning-600);

    &:hover:not(:disabled) {
      background-color: var(--color-warning-700);
      border-color: var(--color-warning-700);
    }
  }

  &.btn-error {
    background-color: var(--color-error-600);
    color: var(--color-white);
    border-color: var(--color-error-600);

    &:hover:not(:disabled) {
      background-color: var(--color-error-700);
      border-color: var(--color-error-700);
    }
  }

  &.btn-outline {
    background-color: transparent;
    color: var(--color-primary-600);
    border-color: var(--color-primary-600);

    &:hover:not(:disabled) {
      background-color: var(--color-primary-600);
      color: var(--color-white);
    }
  }

  &.btn-ghost {
    background-color: transparent;
    color: var(--color-gray-600);
    border-color: transparent;

    &:hover:not(:disabled) {
      background-color: var(--color-gray-100);
      color: var(--color-gray-700);
    }
  }
}

/* === Form Controls === */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-md);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);

  &:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    background-color: var(--color-gray-100);
    color: var(--color-gray-500);
    cursor: not-allowed;
  }

  &.is-invalid {
    border-color: var(--color-error-500);

    &:focus {
      border-color: var(--color-error-500);
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }

  &.is-valid {
    border-color: var(--color-success-500);

    &:focus {
      border-color: var(--color-success-500);
      box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
    }
  }
}

.form-control-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
}

.form-control-lg {
  padding: var(--spacing-4) var(--spacing-5);
  font-size: var(--font-size-base);
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--spacing-10);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-error {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-error-600);
  margin-top: var(--spacing-1);
}

.form-help {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  margin-top: var(--spacing-1);
}

/* === Cards === */
.card {
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-4) var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-2);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-4);
}

/* === Tables === */
.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.table th,
.table td {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--color-gray-200);
}

.table th {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-700);
  background-color: var(--color-gray-50);
}

.table tbody tr:hover {
  background-color: var(--color-gray-50);
}

.table-striped tbody tr:nth-child(odd) {
  background-color: var(--color-gray-50);
}

/* === Badges === */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border-radius: var(--border-radius-full);
  white-space: nowrap;
}

.badge-primary {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

.badge-success {
  background-color: var(--color-success-100);
  color: var(--color-success-800);
}

.badge-warning {
  background-color: var(--color-warning-100);
  color: var(--color-warning-800);
}

.badge-error {
  background-color: var(--color-error-100);
  color: var(--color-error-800);
}

.badge-gray {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
}

/* === Alerts === */
.alert {
  padding: var(--spacing-4);
  border-radius: var(--border-radius-md);
  border: 1px solid transparent;
  margin-bottom: var(--spacing-4);
}

.alert-success {
  background-color: var(--color-success-50);
  border-color: var(--color-success-200);
  color: var(--color-success-800);
}

.alert-warning {
  background-color: var(--color-warning-50);
  border-color: var(--color-warning-200);
  color: var(--color-warning-800);
}

.alert-error {
  background-color: var(--color-error-50);
  border-color: var(--color-error-200);
  color: var(--color-error-800);
}

.alert-info {
  background-color: var(--color-info-50);
  border-color: var(--color-info-200);
  color: var(--color-info-800);
}

/* === Loading Spinner === */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-200);
  border-radius: 50%;
  border-top-color: var(--color-primary-600);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* === Responsive Utilities === */
@media (max-width: 640px) {
  .sm\:d-none { display: none !important; }
  .sm\:d-block { display: block !important; }
  .sm\:flex-column { flex-direction: column !important; }
  .sm\:text-center { text-align: center !important; }
  .sm\:px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
}

@media (min-width: 768px) {
  .md\:d-block { display: block !important; }
  .md\:d-flex { display: flex !important; }
  .md\:flex-row { flex-direction: row !important; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
  .lg\:px-8 { padding-left: var(--spacing-8) !important; padding-right: var(--spacing-8) !important; }
}

/* === Accessibility === */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible for better keyboard navigation */
.focus\:ring:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--color-primary-500);
}

/* === Print Styles === */
@media print {
  .no-print { display: none !important; }

  .btn {
    border: 1px solid var(--color-gray-400) !important;
    background: transparent !important;
    color: var(--color-black) !important;
  }
}
