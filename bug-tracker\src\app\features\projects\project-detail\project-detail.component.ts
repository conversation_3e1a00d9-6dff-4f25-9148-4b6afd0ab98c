import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-project-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="project-detail">
      <div class="project-detail__header">
        <h1>Project Details</h1>
        <div class="d-flex gap-2">
          <a routerLink="edit" class="btn btn-secondary">Edit</a>
          <a routerLink="/projects" class="btn btn-outline">Back</a>
        </div>
      </div>

      <div class="project-detail__content">
        <div class="card">
          <div class="card-body">
            <p class="text-center text-gray-500 py-8">
              Project detail view will be implemented here.
              <br>
              <small>This is a placeholder component.</small>
            </p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .project-detail {
      padding: var(--spacing-6);
    }

    .project-detail__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-6);
    }

    .project-detail__header h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
    }
  `]
})
export class ProjectDetailComponent {
}
