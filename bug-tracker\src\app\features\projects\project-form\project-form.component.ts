import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-project-form',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="project-form">
      <div class="project-form__header">
        <h1>Create Project</h1>
        <a routerLink="/projects" class="btn btn-secondary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 12H5"></path>
            <path d="M12 19l-7-7 7-7"></path>
          </svg>
          Back to Projects
        </a>
      </div>

      <div class="project-form__content">
        <div class="card">
          <div class="card-body">
            <p class="text-center text-gray-500 py-8">
              Project registration form will be implemented here.
              <br>
              <small>This is a placeholder component.</small>
            </p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .project-form {
      padding: var(--spacing-6);
    }

    .project-form__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-6);
    }

    .project-form__header h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
    }
  `]
})
export class ProjectFormComponent {
}
