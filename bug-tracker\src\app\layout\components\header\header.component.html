<header class="header">
  <div class="header__content">
    <!-- Left Section: Menu Toggle & Logo -->
    <div class="header__left">
      <button 
        class="header__menu-toggle btn btn-ghost"
        (click)="toggleSidebar()"
        aria-label="Toggle sidebar">
        <svg class="header__menu-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="3" y1="6" x2="21" y2="6"></line>
          <line x1="3" y1="12" x2="21" y2="12"></line>
          <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
      </button>

      <div class="header__logo">
        <svg class="header__logo-icon" width="32" height="32" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
        </svg>
        <span class="header__logo-text">BugTracker Pro</span>
      </div>
    </div>

    <!-- Right Section: Search & User Menu -->
    <div class="header__right">
      <!-- Search Bar -->
      <div class="header__search">
        <div class="header__search-input-wrapper">
          <svg class="header__search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
          </svg>
          <input 
            type="text" 
            class="header__search-input"
            placeholder="Search bugs, projects..."
            aria-label="Search">
        </div>
      </div>

      <!-- Notifications -->
      <button class="header__notification-btn btn btn-ghost" aria-label="Notifications">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
          <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
        </svg>
        <span class="header__notification-badge">3</span>
      </button>

      <!-- User Menu -->
      <div class="header__user-menu" [class.header__user-menu--open]="isUserMenuOpen">
        <button 
          class="header__user-trigger"
          (click)="toggleUserMenu()"
          aria-label="User menu">
          <div class="header__user-avatar">
            <span class="header__user-initials">{{ getInitials(currentUser.name) }}</span>
          </div>
          <div class="header__user-info">
            <span class="header__user-name">{{ currentUser.name }}</span>
            <span class="header__user-role">{{ currentUser.role }}</span>
          </div>
          <svg class="header__user-chevron" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="6,9 12,15 18,9"></polyline>
          </svg>
        </button>

        <!-- Dropdown Menu -->
        <div class="header__user-dropdown" *ngIf="isUserMenuOpen">
          <div class="header__user-dropdown-header">
            <div class="header__user-dropdown-name">{{ currentUser.name }}</div>
            <div class="header__user-dropdown-email">{{ currentUser.email }}</div>
          </div>
          
          <div class="header__user-dropdown-divider"></div>
          
          <button class="header__user-dropdown-item" (click)="navigateToProfile()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            Profile
          </button>
          
          <button class="header__user-dropdown-item" (click)="navigateToSettings()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
            Settings
          </button>
          
          <div class="header__user-dropdown-divider"></div>
          
          <button class="header__user-dropdown-item header__user-dropdown-item--danger" (click)="logout()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16,17 21,12 16,7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
            Sign out
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Backdrop for mobile user menu -->
  <div 
    class="header__backdrop" 
    *ngIf="isUserMenuOpen"
    (click)="closeUserMenu()">
  </div>
</header>
