.header {
  height: 64px;
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);

  &__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 var(--spacing-6);
    max-width: 100%;
  }

  &__left {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
  }

  &__menu-toggle {
    padding: var(--spacing-2);
    color: var(--color-gray-600);

    &:hover {
      color: var(--color-gray-900);
    }
  }

  &__menu-icon {
    width: 20px;
    height: 20px;
  }

  &__logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    color: var(--color-primary-600);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
  }

  &__logo-icon {
    width: 32px;
    height: 32px;
    color: var(--color-primary-600);
  }

  &__logo-text {
    color: var(--color-gray-900);
  }

  &__right {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
  }

  &__search {
    position: relative;
    
    &-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
    }

    &-icon {
      position: absolute;
      left: var(--spacing-3);
      color: var(--color-gray-400);
      pointer-events: none;
      z-index: 1;
    }

    &-input {
      width: 300px;
      padding: var(--spacing-2) var(--spacing-3) var(--spacing-2) var(--spacing-10);
      font-size: var(--font-size-sm);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      background-color: var(--color-white);
      transition: border-color var(--transition-fast), box-shadow var(--transition-fast);

      &:focus {
        outline: none;
        border-color: var(--color-primary-500);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      &::placeholder {
        color: var(--color-gray-400);
      }
    }
  }

  &__notification-btn {
    position: relative;
    padding: var(--spacing-2);
    color: var(--color-gray-600);

    &:hover {
      color: var(--color-gray-900);
    }
  }

  &__notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--color-error-500);
    color: var(--color-white);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    padding: 2px 6px;
    border-radius: var(--border-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
  }

  &__user-menu {
    position: relative;
  }

  &__user-trigger {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-2);
    border-radius: var(--border-radius-md);
    transition: background-color var(--transition-fast);
    cursor: pointer;
    border: none;
    background: none;

    &:hover {
      background-color: var(--color-gray-100);
    }
  }

  &__user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-full);
    background-color: var(--color-primary-600);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  &__user-initials {
    color: var(--color-white);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
  }

  &__user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
  }

  &__user-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-gray-900);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
  }

  &__user-role {
    font-size: var(--font-size-xs);
    color: var(--color-gray-500);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
  }

  &__user-chevron {
    color: var(--color-gray-400);
    transition: transform var(--transition-fast);
    flex-shrink: 0;

    .header__user-menu--open & {
      transform: rotate(180deg);
    }
  }

  &__user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--spacing-2);
    background-color: var(--color-white);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 240px;
    z-index: var(--z-index-dropdown);
    overflow: hidden;

    &-header {
      padding: var(--spacing-4);
      border-bottom: 1px solid var(--color-gray-200);
    }

    &-name {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-1);
    }

    &-email {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
    }

    &-divider {
      height: 1px;
      background-color: var(--color-gray-200);
      margin: var(--spacing-2) 0;
    }

    &-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      width: 100%;
      padding: var(--spacing-3) var(--spacing-4);
      font-size: var(--font-size-sm);
      color: var(--color-gray-700);
      background: none;
      border: none;
      cursor: pointer;
      transition: background-color var(--transition-fast);
      text-align: left;

      &:hover {
        background-color: var(--color-gray-50);
      }

      &--danger {
        color: var(--color-error-600);

        &:hover {
          background-color: var(--color-error-50);
        }
      }

      svg {
        flex-shrink: 0;
      }
    }
  }

  &__backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--z-index-modal-backdrop);
    background-color: rgba(0, 0, 0, 0.1);
  }

  // Mobile responsive
  @media (max-width: 768px) {
    &__content {
      padding: 0 var(--spacing-4);
    }

    &__search {
      display: none;
    }

    &__user-info {
      display: none;
    }

    &__logo-text {
      display: none;
    }
  }

  // Tablet responsive
  @media (max-width: 1024px) and (min-width: 769px) {
    &__search-input {
      width: 240px;
    }

    &__user-name,
    &__user-role {
      max-width: 100px;
    }
  }
}
