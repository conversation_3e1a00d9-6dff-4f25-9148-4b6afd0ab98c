<aside class="sidebar" [class.sidebar--collapsed]="collapsed">
  <div class="sidebar__content">
    <!-- Navigation -->
    <nav class="sidebar__nav">
      <ul class="sidebar__nav-list">
        <li 
          class="sidebar__nav-item" 
          *ngFor="let item of navigationItems"
          [class.sidebar__nav-item--hidden]="!isItemVisible(item)">
          
          <!-- Parent Item -->
          <div class="sidebar__nav-link-wrapper">
            <a 
              *ngIf="item.route && !item.children"
              [routerLink]="item.route"
              class="sidebar__nav-link"
              [class.sidebar__nav-link--active]="isActive(item.route)"
              [title]="collapsed ? item.label : ''">
              <span class="sidebar__nav-icon" [innerHTML]="getIconSvg(item.icon || '')"></span>
              <span class="sidebar__nav-text" [class.sidebar__nav-text--hidden]="collapsed">
                {{ item.label }}
              </span>
              <span 
                *ngIf="item.badge && !collapsed" 
                class="sidebar__nav-badge">
                {{ item.badge }}
              </span>
            </a>

            <button 
              *ngIf="item.children"
              class="sidebar__nav-link sidebar__nav-link--expandable"
              [class.sidebar__nav-link--active]="isParentActive(item)"
              [class.sidebar__nav-link--expanded]="isExpanded(item)"
              (click)="toggleExpanded(item)"
              [title]="collapsed ? item.label : ''">
              <span class="sidebar__nav-icon" [innerHTML]="getIconSvg(item.icon || '')"></span>
              <span class="sidebar__nav-text" [class.sidebar__nav-text--hidden]="collapsed">
                {{ item.label }}
              </span>
              <span 
                *ngIf="item.badge && !collapsed" 
                class="sidebar__nav-badge">
                {{ item.badge }}
              </span>
              <svg 
                *ngIf="!collapsed"
                class="sidebar__nav-chevron" 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                stroke-width="2">
                <polyline points="9,18 15,12 9,6"></polyline>
              </svg>
            </button>
          </div>

          <!-- Children Items -->
          <ul 
            *ngIf="item.children && isExpanded(item) && !collapsed"
            class="sidebar__nav-sublist">
            <li 
              *ngFor="let child of item.children"
              class="sidebar__nav-subitem"
              [class.sidebar__nav-subitem--hidden]="!isItemVisible(child)">
              <a 
                [routerLink]="child.route"
                class="sidebar__nav-sublink"
                [class.sidebar__nav-sublink--active]="child.route && isActive(child.route)">
                <span class="sidebar__nav-subtext">{{ child.label }}</span>
                <span 
                  *ngIf="child.badge" 
                  class="sidebar__nav-badge sidebar__nav-badge--small">
                  {{ child.badge }}
                </span>
              </a>
            </li>
          </ul>
        </li>
      </ul>
    </nav>

    <!-- Collapsed Menu Indicator -->
    <div *ngIf="collapsed" class="sidebar__collapsed-indicator">
      <div class="sidebar__collapsed-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </div>

  <!-- Resize Handle -->
  <div class="sidebar__resize-handle" *ngIf="!collapsed"></div>
</aside>
