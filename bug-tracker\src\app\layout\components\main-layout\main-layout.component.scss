.main-layout {
  display: grid;
  grid-template-areas: 
    "header header"
    "sidebar content";
  grid-template-columns: 280px 1fr;
  grid-template-rows: 64px 1fr;
  min-height: 100vh;
  background-color: var(--color-gray-50);

  &__header {
    grid-area: header;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-index-fixed);
    background-color: var(--color-white);
    border-bottom: 1px solid var(--color-gray-200);
    box-shadow: var(--shadow-sm);
  }

  &__sidebar {
    grid-area: sidebar;
    position: fixed;
    top: 64px;
    left: 0;
    bottom: 0;
    width: 280px;
    z-index: var(--z-index-sticky);
    background-color: var(--color-white);
    border-right: 1px solid var(--color-gray-200);
    transition: transform var(--transition-base);

    &.collapsed {
      transform: translateX(-240px);
    }
  }

  &__content {
    grid-area: content;
    margin-top: 64px;
    margin-left: 280px;
    min-height: calc(100vh - 64px);
    transition: margin-left var(--transition-base);

    &--expanded {
      margin-left: 40px;
    }

    &-inner {
      padding: var(--spacing-6);
      max-width: 1400px;
      margin: 0 auto;
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "header"
      "content";

    &__sidebar {
      transform: translateX(-100%);
      width: 100%;
      max-width: 320px;

      &:not(.collapsed) {
        transform: translateX(0);
      }
    }

    &__content {
      margin-left: 0;

      &-inner {
        padding: var(--spacing-4);
      }
    }
  }

  // Tablet responsive
  @media (max-width: 1024px) and (min-width: 769px) {
    grid-template-columns: 240px 1fr;

    &__sidebar {
      width: 240px;
    }

    &__content {
      margin-left: 240px;

      &--expanded {
        margin-left: 40px;
      }
    }
  }
}
