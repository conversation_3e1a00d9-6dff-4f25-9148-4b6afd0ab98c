import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-project-list',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="project-list">
      <div class="project-list__header">
        <h1>Projects</h1>
        <a routerLink="/projects/create" class="btn btn-primary">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          Create Project
        </a>
      </div>

      <div class="project-list__content">
        <div class="card">
          <div class="card-body">
            <p class="text-center text-gray-500 py-8">
              Project list will be implemented here.
              <br>
              <small>This is a placeholder component.</small>
            </p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .project-list {
      padding: var(--spacing-6);
    }

    .project-list__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-6);
    }

    .project-list__header h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
    }
  `]
})
export class ProjectListComponent {
}
