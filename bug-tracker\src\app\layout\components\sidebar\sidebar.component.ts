import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

interface NavigationItem {
  label: string;
  icon?: string;
  route?: string;
  children?: NavigationItem[];
  badge?: string | number;
  roles?: string[];
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss'
})
export class SidebarComponent {
  @Input() collapsed = false;

  private router = inject(Router);

  // Mock current user role - will be replaced with real auth service
  currentUserRole = 'Developer';

  navigationItems: NavigationItem[] = [
    {
      label: 'Dashboard',
      icon: 'dashboard',
      route: '/dashboard'
    },
    {
      label: 'Projects',
      icon: 'projects',
      children: [
        { label: 'All Projects', route: '/projects' },
        { label: 'Create Project', route: '/projects/create', roles: ['Admin', 'Project Manager'] },
        { label: 'My Projects', route: '/projects/my' }
      ]
    },
    {
      label: 'Bugs',
      icon: 'bugs',
      children: [
        { label: 'All Bugs', route: '/bugs', badge: '23' },
        { label: 'Report Bug', route: '/bugs/create' },
        { label: 'My Bugs', route: '/bugs/my' },
        { label: 'Assigned to Me', route: '/bugs/assigned', badge: '5' }
      ]
    },
    {
      label: 'Reports',
      icon: 'reports',
      children: [
        { label: 'Bug Reports', route: '/reports/bugs' },
        { label: 'Project Reports', route: '/reports/projects' },
        { label: 'Team Performance', route: '/reports/team', roles: ['Admin', 'Project Manager'] },
        { label: 'Custom Reports', route: '/reports/custom', roles: ['Admin'] }
      ]
    },
    {
      label: 'Admin',
      icon: 'admin',
      roles: ['Admin'],
      children: [
        { label: 'User Management', route: '/admin/users' },
        { label: 'Role Management', route: '/admin/roles' },
        { label: 'System Settings', route: '/admin/settings' },
        { label: 'Integrations', route: '/admin/integrations' }
      ]
    }
  ];

  expandedItems: Set<string> = new Set();

  toggleExpanded(item: NavigationItem) {
    if (item.children) {
      if (this.expandedItems.has(item.label)) {
        this.expandedItems.delete(item.label);
      } else {
        this.expandedItems.add(item.label);
      }
    }
  }

  isExpanded(item: NavigationItem): boolean {
    return this.expandedItems.has(item.label);
  }

  isItemVisible(item: NavigationItem): boolean {
    if (!item.roles || item.roles.length === 0) {
      return true;
    }
    return item.roles.includes(this.currentUserRole);
  }

  isActive(route: string): boolean {
    return this.router.url === route;
  }

  isParentActive(item: NavigationItem): boolean {
    if (!item.children) return false;
    return item.children.some(child => child.route && this.isActive(child.route));
  }

  getIconSvg(iconName: string): string {
    const icons: { [key: string]: string } = {
      dashboard: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <rect x="3" y="3" width="7" height="7"></rect>
        <rect x="14" y="3" width="7" height="7"></rect>
        <rect x="14" y="14" width="7" height="7"></rect>
        <rect x="3" y="14" width="7" height="7"></rect>
      </svg>`,
      projects: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
      </svg>`,
      bugs: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M8 2v4"></path>
        <path d="M16 2v4"></path>
        <rect width="16" height="16" x="4" y="4" rx="2"></rect>
        <path d="M9 9h6"></path>
        <path d="M9 13h6"></path>
      </svg>`,
      reports: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M3 3v18h18"></path>
        <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
      </svg>`,
      admin: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"></path>
      </svg>`
    };
    return icons[iconName] || '';
  }
}
