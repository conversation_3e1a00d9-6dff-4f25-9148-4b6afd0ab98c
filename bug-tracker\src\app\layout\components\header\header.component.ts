import { Component, EventEmitter, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent {
  @Output() sidebarToggle = new EventEmitter<boolean>();

  private router = inject(Router);
  
  isSidebarCollapsed = false;
  isUserMenuOpen = false;

  // Mock user data - will be replaced with real auth service
  currentUser = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Developer',
    avatar: null
  };

  toggleSidebar() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
    this.sidebarToggle.emit(this.isSidebarCollapsed);
  }

  toggleUserMenu() {
    this.isUserMenuOpen = !this.isUserMenuOpen;
  }

  closeUserMenu() {
    this.isUserMenuOpen = false;
  }

  navigateToProfile() {
    this.router.navigate(['/profile']);
    this.closeUserMenu();
  }

  navigateToSettings() {
    this.router.navigate(['/settings']);
    this.closeUserMenu();
  }

  logout() {
    // TODO: Implement logout logic
    console.log('Logout clicked');
    this.closeUserMenu();
  }

  getInitials(name: string): string {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }
}
