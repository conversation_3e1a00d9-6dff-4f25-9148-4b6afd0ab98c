.sidebar {
  width: 280px;
  height: 100%;
  background-color: var(--color-white);
  border-right: 1px solid var(--color-gray-200);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-base);
  overflow: hidden;

  &--collapsed {
    width: 64px;
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__nav {
    flex: 1;
    padding: var(--spacing-4) 0;
    overflow-y: auto;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--color-gray-300);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: var(--color-gray-400);
    }
  }

  &__nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &__nav-item {
    margin-bottom: var(--spacing-1);

    &--hidden {
      display: none;
    }
  }

  &__nav-link-wrapper {
    position: relative;
  }

  &__nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-6);
    color: var(--color-gray-600);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    border: none;
    background: none;
    width: 100%;
    cursor: pointer;
    position: relative;

    &:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
    }

    &--active {
      background-color: var(--color-primary-50);
      color: var(--color-primary-700);
      border-right: 3px solid var(--color-primary-600);

      .sidebar__nav-icon {
        color: var(--color-primary-600);
      }
    }

    &--expandable {
      justify-content: space-between;
    }

    &--expanded {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);

      .sidebar__nav-chevron {
        transform: rotate(90deg);
      }
    }

    .sidebar--collapsed & {
      padding: var(--spacing-3);
      justify-content: center;
    }
  }

  &__nav-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: inherit;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &__nav-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: opacity var(--transition-fast);

    &--hidden {
      opacity: 0;
      width: 0;
    }
  }

  &__nav-badge {
    background-color: var(--color-primary-100);
    color: var(--color-primary-800);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    padding: 2px 6px;
    border-radius: var(--border-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;

    &--small {
      font-size: 10px;
      padding: 1px 4px;
      min-width: 16px;
      height: 16px;
    }
  }

  &__nav-chevron {
    flex-shrink: 0;
    color: var(--color-gray-400);
    transition: transform var(--transition-fast);
  }

  &__nav-sublist {
    list-style: none;
    margin: 0;
    padding: 0;
    background-color: var(--color-gray-50);
    border-top: 1px solid var(--color-gray-200);
    border-bottom: 1px solid var(--color-gray-200);
  }

  &__nav-subitem {
    &--hidden {
      display: none;
    }
  }

  &__nav-sublink {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-2) var(--spacing-6) var(--spacing-2) var(--spacing-12);
    color: var(--color-gray-600);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;

    &:hover {
      background-color: var(--color-gray-100);
      color: var(--color-gray-900);
    }

    &--active {
      background-color: var(--color-primary-50);
      color: var(--color-primary-700);
      border-left-color: var(--color-primary-600);
    }
  }

  &__nav-subtext {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__collapsed-indicator {
    position: absolute;
    bottom: var(--spacing-4);
    left: 50%;
    transform: translateX(-50%);
  }

  &__collapsed-dots {
    display: flex;
    gap: 4px;

    span {
      width: 4px;
      height: 4px;
      background-color: var(--color-gray-400);
      border-radius: 50%;
    }
  }

  &__resize-handle {
    position: absolute;
    top: 0;
    right: -2px;
    bottom: 0;
    width: 4px;
    cursor: col-resize;
    background-color: transparent;
    transition: background-color var(--transition-fast);

    &:hover {
      background-color: var(--color-primary-300);
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    position: fixed;
    top: 64px;
    left: 0;
    bottom: 0;
    z-index: var(--z-index-modal);
    width: 100%;
    max-width: 320px;
    box-shadow: var(--shadow-lg);

    &--collapsed {
      transform: translateX(-100%);
    }
  }

  // Tablet responsive
  @media (max-width: 1024px) and (min-width: 769px) {
    width: 240px;

    &--collapsed {
      width: 56px;
    }

    &__nav-link {
      padding: var(--spacing-2) var(--spacing-4);

      .sidebar--collapsed & {
        padding: var(--spacing-2);
      }
    }

    &__nav-sublink {
      padding: var(--spacing-2) var(--spacing-4) var(--spacing-2) var(--spacing-10);
    }
  }
}
