import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { delay, map } from 'rxjs/operators';
import { 
  User, 
  UserRole, 
  CreateUserRequest, 
  UpdateUserRequest,
  AuthUser,
  getUserInitials,
  formatUserName
} from '../models/user.model';
import { ApiResponse, generateId } from '../models/common.model';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private usersSubject = new BehaviorSubject<User[]>([]);
  private currentUserSubject = new BehaviorSubject<AuthUser | null>(null);
  private storageKey = 'bugtracker_users';
  private currentUserKey = 'bugtracker_current_user';

  users$ = this.usersSubject.asObservable();
  currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    this.loadUsersFromStorage();
    this.loadCurrentUserFromStorage();
  }

  // User CRUD operations
  getUsers(): Observable<User[]> {
    return this.users$.pipe(delay(200));
  }

  getUserById(id: string): User | null {
    return this.usersSubject.value.find(user => user.id === id) || null;
  }

  getUsersByRole(role: UserRole): Observable<User[]> {
    return this.users$.pipe(
      map(users => users.filter(user => user.role === role && user.isActive)),
      delay(150)
    );
  }

  createUser(request: CreateUserRequest): Observable<ApiResponse<User>> {
    const newUser: User = {
      id: generateId(),
      email: request.email,
      firstName: request.firstName,
      lastName: request.lastName,
      fullName: `${request.firstName} ${request.lastName}`,
      role: request.role,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return of(null).pipe(
      delay(300),
      map(() => {
        const currentUsers = this.usersSubject.value;
        const updatedUsers = [...currentUsers, newUser];
        this.usersSubject.next(updatedUsers);
        this.saveUsersToStorage(updatedUsers);

        return {
          success: true,
          data: newUser,
          message: 'User created successfully',
          timestamp: new Date()
        };
      })
    );
  }

  updateUser(id: string, request: UpdateUserRequest): Observable<ApiResponse<User>> {
    return this.users$.pipe(
      delay(300),
      map(users => {
        const userIndex = users.findIndex(u => u.id === id);
        if (userIndex === -1) {
          throw new Error('User not found');
        }

        const updatedUser = {
          ...users[userIndex],
          ...request,
          fullName: request.firstName && request.lastName 
            ? `${request.firstName} ${request.lastName}`
            : users[userIndex].fullName,
          updatedAt: new Date()
        };

        const updatedUsers = [...users];
        updatedUsers[userIndex] = updatedUser;
        
        this.usersSubject.next(updatedUsers);
        this.saveUsersToStorage(updatedUsers);

        return {
          success: true,
          data: updatedUser,
          message: 'User updated successfully',
          timestamp: new Date()
        };
      })
    );
  }

  deleteUser(id: string): Observable<ApiResponse<void>> {
    return this.users$.pipe(
      delay(300),
      map(users => {
        const userExists = users.some(u => u.id === id);
        if (!userExists) {
          throw new Error('User not found');
        }

        const updatedUsers = users.filter(u => u.id !== id);
        this.usersSubject.next(updatedUsers);
        this.saveUsersToStorage(updatedUsers);

        return {
          success: true,
          message: 'User deleted successfully',
          timestamp: new Date()
        };
      })
    );
  }

  // Authentication methods
  getCurrentUser(): AuthUser | null {
    return this.currentUserSubject.value;
  }

  setCurrentUser(user: AuthUser): void {
    this.currentUserSubject.next(user);
    this.saveCurrentUserToStorage(user);
  }

  logout(): void {
    this.currentUserSubject.next(null);
    localStorage.removeItem(this.currentUserKey);
  }

  // Mock authentication - simulate login
  simulateLogin(role: UserRole = UserRole.DEVELOPER): void {
    const mockUser: AuthUser = {
      id: 'current-user-id',
      email: '<EMAIL>',
      fullName: 'John Doe',
      role: role,
      permissions: this.getPermissionsForRole(role),
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token'
    };
    this.setCurrentUser(mockUser);
  }

  // Helper methods
  getUserInitials(user: User | AuthUser): string {
    return getUserInitials(user);
  }

  formatUserName(user: User | AuthUser): string {
    return formatUserName(user);
  }

  private getPermissionsForRole(role: UserRole): string[] {
    // This would typically come from backend
    const permissions: Record<UserRole, string[]> = {
      [UserRole.ADMIN]: ['*'], // All permissions
      [UserRole.PROJECT_MANAGER]: [
        'projects.create', 'projects.update', 'bugs.assign', 'reports.view'
      ],
      [UserRole.DEVELOPER]: [
        'bugs.update', 'bugs.comment', 'projects.view'
      ],
      [UserRole.QA_ENGINEER]: [
        'bugs.create', 'bugs.update', 'bugs.test', 'projects.view'
      ],
      [UserRole.BUSINESS_ANALYST]: [
        'bugs.create', 'bugs.comment', 'projects.view', 'reports.view'
      ]
    };
    return permissions[role] || [];
  }

  private loadUsersFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const users = JSON.parse(stored);
        // Convert date strings back to Date objects
        users.forEach((user: any) => {
          user.createdAt = new Date(user.createdAt);
          user.updatedAt = new Date(user.updatedAt);
          if (user.lastLoginAt) {
            user.lastLoginAt = new Date(user.lastLoginAt);
          }
        });
        this.usersSubject.next(users);
      } else {
        this.initializeMockUsers();
      }
    } catch (error) {
      console.error('Error loading users from storage:', error);
      this.initializeMockUsers();
    }
  }

  private saveUsersToStorage(users: User[]): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(users));
    } catch (error) {
      console.error('Error saving users to storage:', error);
    }
  }

  private loadCurrentUserFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.currentUserKey);
      if (stored) {
        const user = JSON.parse(stored);
        this.currentUserSubject.next(user);
      }
    } catch (error) {
      console.error('Error loading current user from storage:', error);
    }
  }

  private saveCurrentUserToStorage(user: AuthUser): void {
    try {
      localStorage.setItem(this.currentUserKey, JSON.stringify(user));
    } catch (error) {
      console.error('Error saving current user to storage:', error);
    }
  }

  private initializeMockUsers(): void {
    const mockUsers: User[] = [
      {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        fullName: 'Admin User',
        role: UserRole.ADMIN,
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01')
      },
      {
        id: 'user-2',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        fullName: 'John Doe',
        role: UserRole.DEVELOPER,
        isActive: true,
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02')
      },
      {
        id: 'user-3',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        fullName: 'Jane Smith',
        role: UserRole.QA_ENGINEER,
        isActive: true,
        createdAt: new Date('2024-01-03'),
        updatedAt: new Date('2024-01-03')
      },
      {
        id: 'user-4',
        email: '<EMAIL>',
        firstName: 'Mike',
        lastName: 'Wilson',
        fullName: 'Mike Wilson',
        role: UserRole.PROJECT_MANAGER,
        isActive: true,
        createdAt: new Date('2024-01-04'),
        updatedAt: new Date('2024-01-04')
      },
      {
        id: 'user-5',
        email: '<EMAIL>',
        firstName: 'Sarah',
        lastName: 'Johnson',
        fullName: 'Sarah Johnson',
        role: UserRole.BUSINESS_ANALYST,
        isActive: true,
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date('2024-01-05')
      },
      {
        id: 'user-6',
        email: '<EMAIL>',
        firstName: 'David',
        lastName: 'Brown',
        fullName: 'David Brown',
        role: UserRole.DEVELOPER,
        isActive: true,
        createdAt: new Date('2024-01-06'),
        updatedAt: new Date('2024-01-06')
      },
      {
        id: 'user-7',
        email: '<EMAIL>',
        firstName: 'Lisa',
        lastName: 'Davis',
        fullName: 'Lisa Davis',
        role: UserRole.QA_ENGINEER,
        isActive: true,
        createdAt: new Date('2024-01-07'),
        updatedAt: new Date('2024-01-07')
      }
    ];

    this.usersSubject.next(mockUsers);
    this.saveUsersToStorage(mockUsers);

    // Set a default current user for development
    this.simulateLogin(UserRole.DEVELOPER);
  }
}
