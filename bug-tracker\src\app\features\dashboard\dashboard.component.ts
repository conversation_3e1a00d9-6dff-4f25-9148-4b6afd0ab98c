import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="dashboard">
      <div class="dashboard__header">
        <h1>Dashboard</h1>
        <p class="text-gray-600">Welcome to BugTracker Pro</p>
      </div>

      <div class="dashboard__content">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Metrics Cards -->
          <div class="card">
            <div class="card-body">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Total Projects</h3>
              <p class="text-3xl font-bold text-primary">12</p>
              <p class="text-sm text-gray-500 mt-1">+2 this month</p>
            </div>
          </div>

          <div class="card">
            <div class="card-body">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Active Bugs</h3>
              <p class="text-3xl font-bold text-error">23</p>
              <p class="text-sm text-gray-500 mt-1">-5 from last week</p>
            </div>
          </div>

          <div class="card">
            <div class="card-body">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Resolved Bugs</h3>
              <p class="text-3xl font-bold text-success">156</p>
              <p class="text-sm text-gray-500 mt-1">+12 this week</p>
            </div>
          </div>

          <div class="card">
            <div class="card-body">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Team Members</h3>
              <p class="text-3xl font-bold text-info">8</p>
              <p class="text-sm text-gray-500 mt-1">Across all projects</p>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="mt-8">
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Recent Activity</h2>
            </div>
            <div class="card-body">
              <div class="space-y-4">
                <div class="d-flex align-center gap-3">
                  <div class="w-8 h-8 bg-primary-50 rounded-full d-flex align-center justify-center">
                    <span class="text-primary text-sm font-medium">JD</span>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">John Doe created a new bug</p>
                    <p class="text-xs text-gray-500">Login form validation error - 2 hours ago</p>
                  </div>
                </div>

                <div class="d-flex align-center gap-3">
                  <div class="w-8 h-8 bg-success-50 rounded-full d-flex align-center justify-center">
                    <span class="text-success text-sm font-medium">JS</span>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">Jane Smith resolved a bug</p>
                    <p class="text-xs text-gray-500">Dashboard loading issue - 4 hours ago</p>
                  </div>
                </div>

                <div class="d-flex align-center gap-3">
                  <div class="w-8 h-8 bg-warning-50 rounded-full d-flex align-center justify-center">
                    <span class="text-warning text-sm font-medium">MW</span>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">Mike Wilson created a new project</p>
                    <p class="text-xs text-gray-500">E-commerce Platform v2.0 - 1 day ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard {
      padding: var(--spacing-6);
    }

    .dashboard__header {
      margin-bottom: var(--spacing-8);
    }

    .dashboard__header h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-2);
    }

    .space-y-4 > * + * {
      margin-top: var(--spacing-4);
    }

    .w-8 {
      width: 2rem;
    }

    .h-8 {
      height: 2rem;
    }
  `]
})
export class DashboardComponent {
}
